package org.esg.models;

import lombok.Getter;
import net.minecraft.server.v1_8_R3.EnumParticle;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.esg.Effects.ParticleCat;
import org.esg.Main;
import org.esg.Manager.InvulnerabilityManager;
import org.esg.Manager.KnockbackImmunityManager;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.utils.MessageHandler;
import org.esg.Effects.SoundEffects;
import org.esg.utils.NBTUtils;
import org.esg.utils.WeaponUtils;
import org.esg.utils.KnockbackUtil;
import org.esg.ui.EnhancedHUD;
import org.esg.systems.PenetrationSystem;
import org.esg.utils.PvPTagUtil;
import org.esg.utils.StatsUtil;
import org.esg.systems.HeadshotSystem;
import org.esg.systems.VisualEffectsSystem;
import org.esg.listeners.StatsListener;
import org.esg.Manager.ArmorManager;
import org.esg.utils.DamageIndicatorManager;
import org.esg.utils.ImpactSoundManager;
import org.esg.listeners.SniperScopeListener;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Logger;

/**
 * Abstract base class for all weapons in the game.
 *
 * This class handles all weapon functionality including:
 * - Shooting mechanics with projectile physics
 * - Reloading system with timing and progress
 * - Damage calculation with distance falloff
 * - Spread/accuracy system based on weapon type
 * - Multiple projectile support (e.g. for shotguns)
 * - Weapon state management (ammo, reloading status)
 *
 * Each weapon type extends this class and configures its specific properties.
 */
@Getter
public abstract class Weapon {

    private static final Logger LOGGER = Logger.getLogger(Weapon.class.getName());
    private static final double TRACE_STEP = 0.5;
    private static final double HITBOX_WIDTH = 0.2;
    private static final double HITBOX_HEIGHT = 0.2;
    private static final long CACHE_VALIDITY_MS = 1000;
    private static final long CLEAR_MESSAGE_DELAY_TICKS = 40;

    /** Nome da arma exibido para o jogador */
    protected String name;

    /** Tipo da arma (PISTOL, SHOTGUN, RIFLE, SNIPER, SMG) */
    protected WeaponType type;

    /** Tipo de munição que a arma utiliza */
    protected AmmoType ammoType;

    /** Dano base causado por cada projétil */
    protected double damage;

    /** Alcance máximo em blocos que o projétil pode viajar */
    protected double range;

    /** Precisão da arma (0.0 a 1.0) - Quanto maior, menor o espalhamento */
    protected double accuracy;

    /** Taxa de disparo (tiros por segundo) */
    protected double fireRate;

    /** Velocidade do projétil em blocos por segundo */
    protected double projectileSpeed;

    /** Capacidade máxima de munição */
    protected int maxAmmo;

    /** Munição atual */
    protected int currentAmmo;

    /** Tempo de recarga em segundos */
    protected int reloadTime;

    /** Número de projéteis disparados por tiro (1 para maioria das armas, >1 para shotguns) */
    protected int projectileCount;

    /** Flag que indica se a arma está em processo de recarga */
    protected boolean isReloading = false;

    /** Ticks de invulnerabilidade entre tiros */
    protected int customInvulTicks;

    /** Multiplicador de dano para headshots (específico para cada arma) */
    protected double headshotMultiplier = 0.0; // 0.0 significa usar o valor padrão do tipo de arma

    // Parâmetros para controle de espalhamento
    /** Índice de espalhamento base (1.0 = normal, maior = mais espalhamento) */
    protected double spreadIndex = 1.0;

    /** Taxa de progressão do espalhamento com a distância (não usado atualmente) */
    protected double spreadProgressionRate = 1.0;

    /** Aleatoriedade do espalhamento (1.0 = normal, maior = mais aleatório) */
    protected double spreadRandomness = 1.0;

    /** Spread percentual de dano (ex: 0.1 = ±10%) */
    protected double damageSpread = 0.1; // Spread padrão global

    private transient int reloadTaskId = -1;
    private transient String reloadingWeaponId;
    private transient int reloadSlot = -1;

    private static final Map<UUID, Boolean> isFiring = new HashMap<>();
    private static final Map<UUID, Long> lastClickTimes = new HashMap<>();
    private static final Map<UUID, CachedReloadingState> reloadingCache = new HashMap<>();

    // Mapa para rastrear quando o jogador pode atirar novamente após trocar de arma
    // Armazena o tempo (em milissegundos) quando o próximo tiro será permitido
    private static final Map<UUID, Long> nextShotAllowedTime = new HashMap<>();

    protected Weapon(String name, WeaponType type, AmmoType ammoType, double damage, double range,
                     double accuracy, double fireRate, double projectileSpeed, int maxAmmo,
                     int currentAmmo, int reloadTime, int projectileCount) {
        this(name, type, ammoType, damage, range, accuracy, fireRate, projectileSpeed, maxAmmo,
             currentAmmo, reloadTime, projectileCount, 1.0, 1.0, 1.0, 0.0, 0.1);
    }

    protected Weapon(String name, WeaponType type, AmmoType ammoType, double damage, double range,
                     double accuracy, double fireRate, double projectileSpeed, int maxAmmo,
                     int currentAmmo, int reloadTime, int projectileCount, double headshotMultiplier) {
        this(name, type, ammoType, damage, range, accuracy, fireRate, projectileSpeed, maxAmmo,
             currentAmmo, reloadTime, projectileCount, 1.0, 1.0, 1.0, headshotMultiplier, 0.1);
    }

    protected Weapon(String name, WeaponType type, AmmoType ammoType, double damage, double range,
                     double accuracy, double fireRate, double projectileSpeed, int maxAmmo,
                     int currentAmmo, int reloadTime, int projectileCount,
                     double spreadIndex, double spreadProgressionRate, double spreadRandomness,
                     double headshotMultiplier, double damageSpread) {
        this.name = name; this.type = type; this.ammoType = ammoType; this.damage = damage;
        this.range = range; this.accuracy = accuracy; this.fireRate = fireRate;
        this.projectileSpeed = projectileSpeed; this.maxAmmo = maxAmmo; this.currentAmmo = currentAmmo;
        this.reloadTime = reloadTime; this.projectileCount = projectileCount; this.spreadIndex = spreadIndex;
        this.spreadProgressionRate = spreadProgressionRate; this.spreadRandomness = spreadRandomness;
        this.headshotMultiplier = headshotMultiplier; this.damageSpread = damageSpread;
        this.customInvulTicks = calculateInvulTicks(fireRate);
    }

    private static class CachedReloadingState {
        boolean isReloading;
        long timestamp;

        CachedReloadingState(boolean isReloading, long timestamp) {
            this.isReloading = isReloading;
            this.timestamp = timestamp;
        }
    }

    private int calculateInvulTicks(double fireRate) {
        if (fireRate <= 0) return 10;
        double ticksPerShot = 20.0 / fireRate;
        return Math.max(1, (int) Math.floor(ticksPerShot));
    }

    public void shoot(Player player) {
        if (!canShoot(player)) { handleCannotShoot(player); return; }

        org.esg.utils.AnimationBlocker.registerWeaponUse(player);
        this.currentAmmo = org.esg.utils.AmmoCache.decrementAmmo(player, player.getInventory().getItemInHand());
        VisualEffectsSystem.createMuzzleFlash(player, type);
        StatsListener.registerShot(player, type);
        startProjectile(player);
        if (currentAmmo <= 0) handleNoAmmo(player);
    }

    private void startProjectile(Player player) {
        Location handLocation = getHandLocation(player);
        SoundEffects.playShotAt(player, player.getLocation());
        if (type == WeaponType.SHOTGUN) {
            player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.EXPLODE, 0.5f, 1.5f);
        }
        for (int projectileIndex = 0; projectileIndex < projectileCount; projectileIndex++) {
            Vector eyeDirection = calculateShotDirection(player.getEyeLocation(), projectileIndex, player);
            Location targetLocation = player.getEyeLocation().clone().add(eyeDirection.clone().multiply(100));
            Vector handToTarget = targetLocation.toVector().subtract(handLocation.toVector()).normalize();
            Vector initialDirection = handToTarget;
            final Vector projectileDirection = initialDirection.clone(); // Store the projectile direction for knockback
            double distancePerTick = projectileSpeed / 20.0;
            final int numSubSteps = 25;

            // Lançar projétil
            new BukkitRunnable() {
                double distanceTraveled = 0.0;
                Vector currentDirection = initialDirection.clone();
                double currentProjectileDamagePotential = damage; // Dano base do projétil

                @Override
                public void run() {
                    double stepDistance = distancePerTick / numSubSteps;
                    boolean collisionOccurredThisTick = false;
                    Location projectilePositionAtEndOfTick = handLocation.clone().add(currentDirection.clone().multiply(distanceTraveled));

                    for (int i = 0; i < numSubSteps; i++) {
                        Location currentSubStepLoc = handLocation.clone().add(currentDirection.clone().multiply(distanceTraveled + stepDistance));
                        projectilePositionAtEndOfTick = currentSubStepLoc;

                        // Verificar colisão com blocos
                        Block block = currentSubStepLoc.getBlock();
                        if (block.getType().isSolid() && !isThinBlock(block)) {
                            if (PenetrationSystem.canPenetrate(block)) {
                                VisualEffectsSystem.createBlockImpactEffect(currentSubStepLoc, block);
                                currentProjectileDamagePotential = PenetrationSystem.calculatePenetrationDamage(currentProjectileDamagePotential, type);
                                if (currentProjectileDamagePotential < 0.5) {
                                    currentProjectileDamagePotential = 0.5;
                                }
                                if (currentProjectileDamagePotential < 0.05) {
                                    collisionOccurredThisTick = true;
                                    break;
                                }
                            } else {
                                VisualEffectsSystem.createBlockImpactEffect(currentSubStepLoc, block);
                                SoundEffects.playShotAt(player, currentSubStepLoc);
                                collisionOccurredThisTick = true;
                                break;
                            }
                        }

                        // Verificar colisão com entidades
                        for (Entity entity : player.getWorld().getNearbyEntities(currentSubStepLoc, HITBOX_WIDTH, HITBOX_WIDTH, HITBOX_HEIGHT)) {
                            if (entity instanceof LivingEntity && entity != player) {
                                LivingEntity target = (LivingEntity) entity;
                                target.setNoDamageTicks(0);
                                InvulnerabilityManager.resetInvulTicks(target, 0);
                                boolean isHeadshot = HeadshotSystem.isHeadshot(currentSubStepLoc, target, Weapon.this);
                                double damageToApply = calculateDamage(currentProjectileDamagePotential, distanceTraveled + stepDistance);
                                if (isHeadshot) {
                                    damageToApply = HeadshotSystem.calculateHeadshotDamage(damageToApply, Weapon.this, distanceTraveled + stepDistance);
                                    HeadshotSystem.playHeadshotSound(player);
                                    if (target instanceof Player) {
                                        org.esg.stats.StatsManager.registerHeadshot(player, type);
                                    }
                                }
                                VisualEffectsSystem.createEntityImpactEffect(currentSubStepLoc, target, isHeadshot);
                                applyDamageAndKnockback(player, target, damageToApply, isHeadshot, projectileDirection);
                                collisionOccurredThisTick = true;
                                break;
                            }
                        }
                        if (collisionOccurredThisTick) break;
                        distanceTraveled += stepDistance;
                        if (distanceTraveled >= range) break;
                    }
                    // Enviar partícula do projétil (apenas se não colidiu e dentro do alcance)
                    if (!collisionOccurredThisTick && distanceTraveled < range) {
                        ParticleCat.sendParticle(EnumParticle.SMOKE_NORMAL, projectilePositionAtEndOfTick, 0, 0, 0, 0, 1);
                    }
                    if (collisionOccurredThisTick || distanceTraveled >= range) {
                        cancel();
                    }
                }
            }.runTaskTimer(Main.getPlugin(), 0L, 1L);
        }
    }

    private double calculateDamage(double currentBaseDamage, double distanceTraveled) {
        double ratio = distanceTraveled / range;
        double multiplier;
        switch (type) {
            case SHOTGUN: multiplier = Math.max(0.2, Math.min(2.0, 2.0 - (ratio * 1.8))); break;
            case SNIPER: multiplier = Math.max(0.5, Math.min(1.6, 0.5 + (ratio * 1.1))); break;
            case SMG: multiplier = 1.0 - (ratio * 0.5); break;
            case PISTOL: multiplier = 1.0 - (ratio * 0.3); break;
            case RIFLE: multiplier = 1.0 - (ratio * 0.2); break;
            default: multiplier = 1.0; break;
        }
        double damage = currentBaseDamage * multiplier * (1.0 + ((Math.random() * 2 - 1) * damageSpread));
        return Math.max(damage, (type == WeaponType.SHOTGUN && projectileCount > 1) ? 0.5 : 1.0);
    }

    private void applyDamageAndKnockback(Player player, LivingEntity target, double damage, boolean isHeadshot, Vector projectileDirection) {
        Vector knockback = null;
        boolean shouldApplyKnockback = false;

        if (target instanceof Player) {
            Player targetPlayer = (Player) target;
            if (!KnockbackImmunityManager.isImmune(targetPlayer)) {
                knockback = "AR-15".equals(name) ?
                    org.esg.weapons.AR15.calculateAR15Knockback(targetPlayer, player, projectileDirection) :
                    KnockbackUtil.getKnockback(target.getLocation(), player, name, projectileDirection);
                shouldApplyKnockback = true;
                KnockbackImmunityManager.applyImmunity(targetPlayer, name, type);
            }
        } else {
            knockback = KnockbackUtil.getKnockback(target.getLocation(), player, null, projectileDirection);
            shouldApplyKnockback = true;
        }

        // Calcular o dano considerando a armadura do alvo
        double finalDamage = damage;
        if (target instanceof Player) {
            Player targetPlayer = (Player) target;

            // Verificar se o alvo tem armadura
            boolean hasArmor = targetPlayer.getInventory().getHelmet() != null ||
                               targetPlayer.getInventory().getChestplate() != null ||
                               targetPlayer.getInventory().getLeggings() != null ||
                               targetPlayer.getInventory().getBoots() != null;

            // Calcular dano reduzido pela armadura
            finalDamage = ArmorManager.calculateReducedDamage(targetPlayer, damage);

            // Reproduzir sons de impacto distintos
            ImpactSoundManager.playImpactSound(player, targetPlayer, isHeadshot, this);

            // Mostrar indicador direcional de dano ao jogador atingido
            DamageIndicatorManager.showDirectionalIndicator(targetPlayer, player.getLocation());

            // Mostrar efeito de tela vermelha quando com pouca vida
            DamageIndicatorManager.showLowHealthEffect(targetPlayer, finalDamage);

            // Aplicar PvP tag quando um jogador atira em outro jogador
            PvPTagUtil.applyPvPTag(player, targetPlayer);

            // Registrar o dano nas estatísticas
            StatsUtil.registerWeaponDamage(player, targetPlayer, this, isHeadshot);
        }

        // Aplicar o dano e o knockback separadamente
        // O knockback permanece o mesmo, independentemente da armadura

        // Tratamento especial para entidades que precisam de manipulação direta de saúde
        boolean isSpecialEntity = false;

        // Verificar se é uma entidade especial
        if (target.getType() == org.bukkit.entity.EntityType.ENDER_DRAGON) {
            isSpecialEntity = true;

            // Usar o método específico para causar dano ao EnderDragon
            org.bukkit.entity.EnderDragon dragon = (org.bukkit.entity.EnderDragon) target;

            // Aplicar dano diretamente à saúde do EnderDragon
            double currentHealth = dragon.getHealth();
            double newHealth = Math.max(0, currentHealth - finalDamage);
            dragon.setHealth(newHealth);

            // Registrar o dano no log
            LOGGER.info("Applied " + finalDamage + " damage to EnderDragon. Health: " +
                        currentHealth + " -> " + newHealth);

            // Efeito visual para indicar o dano
            target.getWorld().playEffect(target.getLocation(), org.bukkit.Effect.SMOKE, 0);

            // Efeito sonoro para indicar o dano
            target.getWorld().playSound(target.getLocation(), org.bukkit.Sound.ENDERDRAGON_GROWL, 0.5f, 1.5f);
        }
        // Verificar se é um Wither (outro boss que pode precisar de tratamento especial)
        else if (target.getType() == org.bukkit.entity.EntityType.WITHER) {
            isSpecialEntity = true;

            // Usar o método específico para causar dano ao Wither
            org.bukkit.entity.Wither wither = (org.bukkit.entity.Wither) target;

            // Aplicar dano diretamente à saúde do Wither
            double currentHealth = wither.getHealth();
            double newHealth = Math.max(0, currentHealth - finalDamage);
            wither.setHealth(newHealth);

            // Registrar o dano no log
            LOGGER.info("Applied " + finalDamage + " damage to Wither. Health: " +
                        currentHealth + " -> " + newHealth);

            // Efeito visual para indicar o dano
            target.getWorld().playEffect(target.getLocation(), org.bukkit.Effect.SMOKE, 0);
        }
        // Método normal para outras entidades
        else {
            // Aplicar dano diretamente para evitar interferência de outros sistemas
            if (target instanceof Player) {
                // Para players, usar o método damage com o player como damager para manter compatibilidade
                target.damage(finalDamage, player);
            } else {
                // Para outras entidades, usar damage simples
                target.damage(finalDamage);
            }
        }

        // Apply knockback using the new system (except for special entities)
        if (!isSpecialEntity && shouldApplyKnockback && knockback != null) {
            // Criar uma cópia final da variável knockback para usar no runnable
            final Vector finalKnockback = knockback.clone();
            // Aplicar knockback com um pequeno delay para garantir que seja aplicado após o dano
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    if (target.isValid() && !target.isDead()) {
                        target.setVelocity(finalKnockback);
                    }
                }
            }.runTaskLater(org.esg.Main.getPlugin(), 1L); // 1 tick de delay
        }

        // Registrar o dano no HUD aprimorado, incluindo informação de headshot e entidade atingida
        EnhancedHUD.registerDamage(player, target, finalDamage, isHeadshot);
    }

    // Sobrecarga para compatibilidade com código existente
    private void applyDamageAndKnockback(Player player, LivingEntity target, double damage) {
        applyDamageAndKnockback(player, target, damage, false, null);
    }

    // Sobrecarga para compatibilidade com código existente
    private void applyDamageAndKnockback(Player player, LivingEntity target, double damage, boolean isHeadshot) {
        applyDamageAndKnockback(player, target, damage, isHeadshot, null);
    }

    /**
     * Calcula a direção do tiro com base na precisão da arma e no índice do projétil.
     *
     * Este método implementa o sistema de espalhamento das armas:
     * - Quanto menor a precisão da arma, maior o espalhamento
     * - Armas com múltiplos projéteis (como shotguns) têm um padrão de espalhamento em cone
     * - O primeiro projétil sempre vai quase reto, com pequena variação
     * - Os projéteis são distribuídos em camadas para criar um padrão natural
     *
     * @param eyeLocation A posição dos olhos do jogador
     * @param projectileIndex O índice do projétil (0 para o primeiro)
     * @param player O jogador que está disparando a arma
     * @return Um vetor normalizado representando a direção do projétil
     */
    private Vector calculateShotDirection(Location eyeLocation, int projectileIndex, Player player) {
        Vector direction = eyeLocation.getDirection().normalize();
        double baseSpread;
        // Se for sniper e não estiver scoped, ou se for DMR, aplica precisão baixa
        if ((type == WeaponType.SNIPER && (player == null || !SniperScopeListener.isScoped(player))) ||
            (name != null && name.equalsIgnoreCase("DMR"))) {
            baseSpread = (1.0 - 0.2) * 0.1; // Precisão baixa (0.2)
        } else {
            baseSpread = (1.0 - accuracy) * 0.2;
        }

        // Caso especial para armas com múltiplos projéteis (como shotgun)
        if (projectileCount > 1 && projectileIndex > 0) {
            // Calcular ângulo e raio para distribuição em círculo
            double angle = 2.0 * Math.PI * ((double)projectileIndex / projectileCount);

            // Distribuir em camadas para padrão mais natural
            double radius;
            if (projectileIndex <= projectileCount / 3) {
                radius = baseSpread * 0.5; // Camada interna
            } else if (projectileIndex <= 2 * projectileCount / 3) {
                radius = baseSpread * 0.8; // Camada média
            } else {
                radius = baseSpread; // Camada externa
            }

            // Pequena variação aleatória
            radius *= (1.0 + (Math.random() - 0.5) * 0.2);

            // Calcular deslocamento e aplicar à direção
            double offsetX = Math.cos(angle) * radius;
            double offsetY = Math.sin(angle) * radius;

            Vector right = new Vector(-direction.getZ(), 0, direction.getX()).normalize();
            Vector up = right.getCrossProduct(direction).normalize();

            return direction.clone().add(right.multiply(offsetX)).add(up.multiply(offsetY)).normalize();
        } else {
            // Para projétil único ou primeiro projétil
            double randomFactor = baseSpread * 0.3;
            double offsetX = (Math.random() - 0.5) * 2 * randomFactor;
            double offsetY = (Math.random() - 0.5) * 2 * randomFactor;

            Vector right = new Vector(-direction.getZ(), 0, direction.getX()).normalize();
            Vector up = right.getCrossProduct(direction).normalize();

            return direction.clone().add(right.multiply(offsetX)).add(up.multiply(offsetY)).normalize();
        }
    }

    private void handleCannotShoot(Player player) {
        if (isReloading(player)) {
            MessageHandler.sendReloading(player);
        } else {
            MessageHandler.sendNoAmmo(player);
            SoundEffects.playError(player);
        }
        isFiring.put(player.getUniqueId(), false);
        WeaponUtils.updateWeaponInHand(player, this);
    }

    private void handleNoAmmo(Player player) {
        MessageHandler.sendNoAmmo(player);
        SoundEffects.playError(player);
        // Não cancela o disparo aqui para permitir que o jogador recarregue com o clique direito
        // isFiring.put(player.getUniqueId(), false);

        // Atualiza o item na mão do jogador para refletir o estado atual da arma
        WeaponUtils.updateWeaponInHand(player, this);

        // Registra no log para depuração
        LOGGER.info("NO AMMO: Player " + player.getName() + " is out of ammo with weapon " + name +
                    ". Current ammo: " + currentAmmo + ", isReloading: " + isReloading);
    }


    private Location getHandLocation(Player player) {
        Location eyeLocation = player.getEyeLocation();
        Vector direction = eyeLocation.getDirection().normalize();

        // Vetor perpendicular à direita
        Vector rightVector = new Vector(-direction.getZ(), 0, direction.getX()).normalize();

        // Começa nos olhos
        Location neckLocation = eyeLocation.clone();

        // Ajustar para cima (quase na altura dos olhos, levemente acima do ombro)
        neckLocation.setY(neckLocation.getY() - 0.05); // -0.05 = quase altura dos olhos (pescoco)

        // Ajustar para a direita, mas menos (mais próximo do centro do corpo)
        neckLocation.add(rightVector.multiply(0.15)); // 0.15 = mais centralizado

        // Ajustar levemente para frente
        neckLocation.add(direction.clone().multiply(0.2));

        return neckLocation;
    }

    private Vector calculateShotDirection(Location eyeLocation) {
        // Chama o novo método com projectileIndex = 0 para manter compatibilidade
        return calculateShotDirection(eyeLocation, 0, null);
    }

    public void reload(Player player) {
        // Cancelar o estado de disparo imediatamente
        UUID playerUUID = player.getUniqueId();
        isFiring.put(playerUUID, false);

        if (!canReload(player)) {
            if (isReloading(player)) {
                LOGGER.info("Reload blocked: already reloading for player " + player.getName());
                MessageHandler.sendAlreadyReloading(player);
            } else if (currentAmmo >= maxAmmo) {
                LOGGER.info("Reload blocked: ammo full for player " + player.getName());
                MessageHandler.sendFullAmmo(player);
            }
            return;
        }
        startReload(player);
    }

    /**
     * Inicia o processo de recarga para um jogador.
     * Este método foi melhorado para garantir que o estado inicial da recarga seja consistente.
     *
     * @param player O jogador
     */
    private void startReload(Player player) {
        LOGGER.info("Starting reload for player: " + player.getName());

        // Cancelar qualquer recarga anterior que possa estar em andamento
        if (reloadTaskId != -1) {
            player.getServer().getScheduler().cancelTask(reloadTaskId);
            reloadTaskId = -1;
            LOGGER.info("Cancelled previous reload task for player: " + player.getName());
        }

        // Definir o estado de recarga
        isReloading = true;

        // Obter informações sobre o item na mão
        ItemStack itemInHand = player.getInventory().getItemInHand();
        if (itemInHand == null) {
            LOGGER.warning("Cannot start reload: player " + player.getName() + " has no item in hand");
            return;
        }

        // Armazenar o ID da arma e o slot para referência futura
        reloadingWeaponId = WeaponUtils.getWeaponId(itemInHand);
        reloadSlot = player.getInventory().getHeldItemSlot();

        LOGGER.info("Reload info: weapon=" + name +
                ", reloadingWeaponId=" + reloadingWeaponId +
                ", reloadSlot=" + reloadSlot);

        // Garantir que o jogador não esteja atirando durante a recarga
        UUID playerUUID = player.getUniqueId();
        isFiring.put(playerUUID, false);

        // Atualizar o item na mão para refletir o estado de recarga
        WeaponUtils.updateWeaponInHand(player, this);

        // Atualizar o cache de estado de recarga
        reloadingCache.put(playerUUID, new CachedReloadingState(true, System.currentTimeMillis()));

        // Registrar o início da recarga no HUD aprimorado
        EnhancedHUD.startReload(player);

        // Agendar a tarefa de recarga
        scheduleReloadTask(player);
    }

    private int validateReloadSlot(int slot) {
        if (slot < 0 || slot > 8) {
            LOGGER.warning("Invalid reloadSlot detected: " + slot + ", setting to 0");
            return 0;
        }
        return slot;
    }

    /**
     * Agenda a tarefa de recarga para um jogador.
     * Este método foi melhorado para ser mais robusto e evitar problemas de recarga interrompida.
     *
     * @param player O jogador
     */
    private void scheduleReloadTask(Player player) {
        // Garantir que o estado de recarga seja consistente
        isReloading = true;

        // Atualizar o item na mão do jogador para refletir o estado de recarga
        WeaponUtils.updateWeaponInHand(player, this);

        // Atualizar o cache de estado de recarga
        reloadingCache.put(player.getUniqueId(), new CachedReloadingState(true, System.currentTimeMillis()));

        // Mostrar mensagem de início de recarga
        MessageHandler.sendReloading(player);
        SoundEffects.playReloadStart(player);

        // Registrar o início da recarga no log
        LOGGER.info("Starting reload for player " + player.getName() +
                ", weapon: " + name +
                ", reloadTime: " + reloadTime +
                " seconds, reloadingWeaponId: " + reloadingWeaponId);

        // Agendar a conclusão da recarga após o tempo definido
        reloadTaskId = new BukkitRunnable() {
            int ticksLeft = reloadTime * 20;
            int totalTicks = reloadTime * 20;
            int lastLoggedTick = ticksLeft;

            @Override
            public void run() {
                // Verificar se o jogador ainda está online
                if (!player.isOnline()) {
                    LOGGER.info("Cancelling reload: player " + player.getName() + " is offline");
                    cancel();
                    return;
                }

                // Verificar se o jogador ainda está com a arma e recarregando
                boolean stillReloading = isReloading(player);
                boolean weaponInHand = isWeaponInHand(player);

                // Registrar no log a cada 20 ticks (1 segundo) ou quando houver uma mudança
                if (ticksLeft % 20 == 0 || lastLoggedTick - ticksLeft >= 20 || !stillReloading || !weaponInHand) {
                    // Comentado para reduzir log spam
                    // LOGGER.info("Reload progress for " + player.getName() +
                    //             ": " + ticksLeft + "/" + totalTicks + " ticks, " +
                    //             "isReloading=" + isReloading + ", " +
                    //             "weaponInHand=" + isWeaponInHand(player));
                    lastLoggedTick = ticksLeft;
                }

                if (!stillReloading || !weaponInHand) {
                    LOGGER.info("Cancelling reload: stillReloading=" + stillReloading +
                            ", weaponInHand=" + weaponInHand);
                    cancelReload(player);
                    cancel();
                    return;
                }

                // Atualizar o progresso da recarga no HUD
                float progress = 1.0f - ((float) ticksLeft / totalTicks);
                EnhancedHUD.updateReloadProgress(player, progress);

                // Concluir a recarga quando o tempo acabar
                if (ticksLeft <= 0) {
                    LOGGER.info("Reload complete for player " + player.getName());
                    completeReload(player);
                    cancel();
                    return;
                }

                // Atualizar o estado da arma periodicamente para garantir consistência
                if (ticksLeft % 10 == 0) { // A cada 10 ticks (0.5 segundos)
                    WeaponUtils.updateWeaponInHand(player, Weapon.this);
                    reloadingCache.put(player.getUniqueId(), new CachedReloadingState(true, System.currentTimeMillis()));
                }

                ticksLeft--;
            }
        }.runTaskTimer(Main.getPlugin(), 0L, 1L).getTaskId();


    }

    public boolean canShoot(Player player) {
        // Verificar se o jogador pode atirar baseado no timing da arma após trocar de slot
        if (!canShootAfterWeaponSwitch(player)) {
            // Mostrar mensagem e efeito sonoro para indicar que o jogador precisa esperar o timing da arma
            MessageHandler.sendActionBar(player, "§c" + name + " cycling...");
            player.playSound(player.getLocation(), org.bukkit.Sound.NOTE_BASS, 0.5f, 0.5f);
            return false;
        }

        // Verificar a munição usando o AmmoCache para o item na mão do jogador
        ItemStack itemInHand = player.getInventory().getItemInHand();
        int cachedAmmo = itemInHand != null ? org.esg.utils.AmmoCache.getAmmo(player, itemInHand) : 0;

        // Se o AmmoCache retornar -1 (item não é uma arma), usar o valor interno
        if (cachedAmmo == -1) cachedAmmo = currentAmmo;

        return cachedAmmo > 0 && !isReloading(player);
    }

    /**
     * Verifica se a arma pode ser recarregada.
     * @param player O jogador que está tentando recarregar
     * @return true se a arma pode ser recarregada, false caso contrário
     */
    public boolean canReload(Player player) {
        // Verificar a munição usando o AmmoCache para o item na mão do jogador
        ItemStack itemInHand = player.getInventory().getItemInHand();
        int cachedAmmo = itemInHand != null ? org.esg.utils.AmmoCache.getAmmo(player, itemInHand) : 0;

        // Se o AmmoCache retornar -1 (item não é uma arma), usar o valor interno
        if (cachedAmmo == -1) cachedAmmo = currentAmmo;

        return !isReloading(player) && cachedAmmo < maxAmmo;
    }

    /**
     * Verifica se a arma está em processo de recarga para um jogador específico.
     * Este método foi melhorado para ser mais confiável e evitar problemas de recarga interrompida.
     *
     * @param player O jogador
     * @return true se a arma estiver recarregando, false caso contrário
     */
    public boolean isReloading(Player player) {
        UUID playerId = player.getUniqueId();

        // Verificar o cache primeiro para evitar operações desnecessárias
        CachedReloadingState cached = reloadingCache.get(playerId);
        if (cached != null && (System.currentTimeMillis() - cached.timestamp) < CACHE_VALIDITY_MS) {
            return cached.isReloading;
        }

        // Se a tarefa de recarga estiver ativa, considerar que está recarregando
        if (reloadTaskId != -1 && reloadingWeaponId != null &&
            WeaponUtils.isSameWeapon(player.getInventory().getItemInHand(), reloadingWeaponId)) {
            // Atualizar o cache e retornar true
            reloadingCache.put(playerId, new CachedReloadingState(true, System.currentTimeMillis()));
            return true;
        }

        // Verificar o item na mão do jogador
        ItemStack itemInHand = player.getInventory().getItemInHand();
        boolean result;

        if (itemInHand != null && WeaponUtils.isSameWeapon(itemInHand, reloadingWeaponId)) {
            // O item na mão é a arma que está sendo recarregada
            Weapon weaponFromItem = WeaponUtils.getWeaponFromItem(itemInHand, player);

            if (weaponFromItem != null) {
                // Usar o estado de recarga da instância da arma no item
                // Comentado para reduzir log spam
                // LOGGER.info("isReloading for player " + player.getName() +
                //             ": weaponFromItem.isReloading=" + weaponFromItem.isReloading +
                //             ", currentAmmo=" + weaponFromItem.currentAmmo);
                result = weaponFromItem.isReloading;
            } else {
                // Fallback para o estado local
                // Comentado para reduzir log spam
                // LOGGER.info("isReloading for player " + player.getName() +
                //             ": fallback to local isReloading=" + isReloading +
                //             ", currentAmmo=" + currentAmmo);
                result = isReloading;
            }
        } else {
            // A arma não está na mão do jogador
            if (reloadingWeaponId != null) {
                // Comentado para reduzir log spam
                // LOGGER.info("isReloading for player " + player.getName() +
                //             ": weapon not in hand, expected=" + reloadingWeaponId +
                //             ", found=" + (itemInHand != null ? WeaponUtils.getWeaponId(itemInHand) : "null"));
            }

            // Se a arma não está na mão, não está recarregando
            isReloading = false;
            result = false;
        }

        // Atualizar o cache com o resultado
        reloadingCache.put(playerId, new CachedReloadingState(result, System.currentTimeMillis()));
        return result;
    }

    private boolean checkReloadingState(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInHand();
        if (itemInHand == null || !WeaponUtils.isSameWeapon(itemInHand, reloadingWeaponId)) {
            // Comentado para reduzir log spam
            // LOGGER.info("isReloading for player " + player.getName() +
            //             ": fallback to local isReloading=" + isReloading + ", currentAmmo=" + currentAmmo);
            return isReloading;
        }

        Weapon weaponFromItem = WeaponUtils.getWeaponFromItem(itemInHand, player);
        if (weaponFromItem != null) {
            // Comentado para reduzir log spam
            // LOGGER.info("isReloading for player " + player.getName() +
            //             ": weaponFromItem.isReloading=" + weaponFromItem.isReloading +
            //             ", currentAmmo=" + weaponFromItem.currentAmmo);
            return weaponFromItem.isReloading;
        }

        // Comentado para reduzir log spam
        // LOGGER.info("isReloading for player " + player.getName() +
        //             ": fallback to local isReloading=" + isReloading + ", currentAmmo=" + currentAmmo);
        return isReloading;
    }

    public void cancelReload(Player player) {
        if (!isReloading(player)) return;
        isReloading = false;
        isFiring.put(player.getUniqueId(), false);
        if (reloadTaskId != -1) {
            player.getServer().getScheduler().cancelTask(reloadTaskId);
            reloadTaskId = -1;
        }

        // Atualiza o item no slot de recarregamento
        if (reloadSlot >= 0 && reloadSlot <= 8) {
            ItemStack itemInSlot = player.getInventory().getItem(reloadSlot);
            if (itemInSlot != null && WeaponUtils.isSameWeapon(itemInSlot, reloadingWeaponId)) {
                WeaponUtils.updateWeaponInSlot(player, reloadSlot, this);
                // Comentado para reduzir log spam
                // LOGGER.info("Updated NBT in slot " + reloadSlot + " with isReloading=false");
            } else {
                // Comentado para reduzir log spam
                // LOGGER.warning("Weapon no longer in slot " + reloadSlot + " during cancelReload for player: " + player.getName());
            }
        } else {
            // Comentado para reduzir log spam
            // LOGGER.warning("Invalid reloadSlot: " + reloadSlot + ", skipping update");
        }

        ItemStack itemInHand = player.getInventory().getItemInHand();
        if (itemInHand != null && WeaponUtils.isSameWeapon(itemInHand, reloadingWeaponId)) {
            WeaponUtils.updateWeaponInHand(player, this);
        }

        MessageHandler.sendReloadCancelled(player);
        reloadingCache.put(player.getUniqueId(), new CachedReloadingState(false, System.currentTimeMillis()));

        reloadSlot = -1;
        reloadingWeaponId = null;

        scheduleClearMessage(player);
    }

    private void scheduleClearMessage(Player player) {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!isReloading(player)) {
                    MessageHandler.clear(player);
                }
            }
        }.runTaskLater(Main.getPlugin(), CLEAR_MESSAGE_DELAY_TICKS);
    }

    public void setCurrentAmmo(int currentAmmo) {
        this.currentAmmo = Math.max(0, Math.min(currentAmmo, maxAmmo));
    }

    public void setReloading(boolean reloading) {
        this.isReloading = reloading;
        if (reloadingWeaponId != null) {
            Player player = Main.getPlugin().getServer().getPlayer(reloadingWeaponId);
            if (player != null) {
                reloadingCache.put(player.getUniqueId(), new CachedReloadingState(reloading, System.currentTimeMillis()));
            }
        }
    }

    public String getReloadingWeaponId() {
        return reloadingWeaponId;
    }

    private void completeReload(Player player) {
        if (!isReloading(player) || !isWeaponInHand(player)) return;
        currentAmmo = maxAmmo;
        isReloading = false;
        isFiring.put(player.getUniqueId(), false);

        // Atualizar o cache de munição para evitar inconsistências
        ItemStack itemInHand = player.getInventory().getItemInHand();
        if (itemInHand != null) {
            // Atualizar o cache com a munição máxima
            org.esg.utils.AmmoCache.setAmmo(player, itemInHand, maxAmmo);

            // Forçar a sincronização com o NBT para garantir persistência
            org.esg.utils.AmmoCache.syncWithNBT(player, itemInHand, true);
        }

        // Atualizar o item na mão (isso também atualiza o NBT)
        WeaponUtils.updateWeaponInHand(player, this);

        SoundEffects.playReloadComplete(player);
        MessageHandler.sendReloadComplete(player, currentAmmo, maxAmmo);

        reloadingCache.put(player.getUniqueId(), new CachedReloadingState(false, System.currentTimeMillis()));

        reloadTaskId = -1;
        reloadSlot = -1;
        reloadingWeaponId = null;
    }

    /**
     * Verifica se a arma que está sendo recarregada ainda está na mão do jogador.
     * Este método foi melhorado para ser mais tolerante e evitar cancelamentos de recarga desnecessários.
     *
     * @param player O jogador
     * @return true se a arma estiver na mão, false caso contrário
     */
    private boolean isWeaponInHand(Player player) {
        // Se não há ID de arma em recarga, considerar que não está na mão
        if (reloadingWeaponId == null) {
            return false;
        }

        // Verificar o item na mão do jogador
        ItemStack item = player.getInventory().getItemInHand();

        // Verificar se o item é a mesma arma que está sendo recarregada
        boolean isSameWeapon = WeaponUtils.isSameWeapon(item, reloadingWeaponId);

        // Registrar no log para depuração
        if (!isSameWeapon) {
            // Comentado para reduzir log spam
            // LOGGER.info("Weapon not in hand for player " + player.getName() +
            //             ". Expected: " + reloadingWeaponId + ", Found: " +
            //             (item != null ? WeaponUtils.getWeaponId(item) : "null"));
        }

        return isSameWeapon;
    }

    public static Map<UUID, Boolean> getIsFiring() {
        return isFiring;
    }

    public static Map<UUID, Long> getLastClickTimes() {
        return lastClickTimes;
    }

    public double getSpreadIndex() {
        return spreadIndex;
    }

    /**
     * Retorna o alcance máximo da arma em blocos.
     *
     * @return O alcance máximo da arma
     */
    public double getRange() {
        return range;
    }

    /**
     * Retorna o multiplicador de dano para headshots desta arma.
     * Se o multiplicador for 0.0, retorna o valor padrão para o tipo de arma.
     *
     * @return O multiplicador de dano para headshots
     */
    public double getHeadshotMultiplier() {
        // Se o multiplicador for 0.0, usar o valor padrão para o tipo de arma
        if (headshotMultiplier <= 0.0) {
            switch (type) {
                case SNIPER: return HeadshotSystem.SNIPER_HEADSHOT_MULTIPLIER;
                case RIFLE: return HeadshotSystem.RIFLE_HEADSHOT_MULTIPLIER;
                case SMG: return HeadshotSystem.SMG_HEADSHOT_MULTIPLIER;
                case PISTOL: return HeadshotSystem.PISTOL_HEADSHOT_MULTIPLIER;
                case SHOTGUN: return HeadshotSystem.SHOTGUN_HEADSHOT_MULTIPLIER;
                default: return 2.0; // Valor padrão
            }
        }
        return headshotMultiplier;
    }

    public double getSpreadProgressionRate() {
        return spreadProgressionRate;
    }

    public double getSpreadRandomness() {
        return spreadRandomness;
    }

    /**
     * Retorna o tipo da arma.
     *
     * @return O tipo da arma (PISTOL, SMG, RIFLE, SHOTGUN, SNIPER)
     */
    public WeaponType getType() {
        return type;
    }

    /**
     * Retorna o nome da arma.
     *
     * @return O nome da arma
     */
    public String getName() {
        return name;
    }

    /**
     * Retorna o tempo de recarga da arma em segundos.
     *
     * @return O tempo de recarga em segundos
     */
    public int getReloadTime() {
        return reloadTime;
    }

    /**
     * Interrompe o disparo da arma para um jogador.
     *
     * @param player O jogador
     */
    public void stopFiring(Player player) {
        UUID playerUUID = player.getUniqueId();
        isFiring.put(playerUUID, false);
    }

    /**
     * Interrompe todos os disparos para um jogador.
     * Este método é estático para poder ser chamado sem uma instância específica de arma.
     *
     * @param player O jogador
     */
    public static void stopAllFiring(Player player) {
        UUID playerUUID = player.getUniqueId();
        isFiring.put(playerUUID, false);
    }



    /**
     * Verifica se o jogador pode atirar baseado no timing natural da arma após trocar de slot.
     * Este método força o jogador a respeitar o intervalo entre tiros da arma.
     *
     * @param player O jogador
     * @return true se o jogador pode atirar, false se precisa esperar o timing da arma
     */
    public boolean canShootAfterWeaponSwitch(Player player) {
        UUID playerUUID = player.getUniqueId();
        Long nextShotTime = nextShotAllowedTime.get(playerUUID);

        if (nextShotTime == null) {
            return true; // Primeira vez ou sem restrição
        }

        long currentTime = System.currentTimeMillis();
        boolean canShoot = currentTime >= nextShotTime;

        // Limpar o tempo expirado
        if (canShoot) {
            nextShotAllowedTime.remove(playerUUID);
        }

        return canShoot;
    }

    /**
     * Registra o momento em que um jogador trocou de arma e define quando ele pode atirar novamente.
     * Este método deve ser chamado sempre que o jogador trocar de slot.
     * Força o jogador a esperar o tempo natural entre tiros da nova arma.
     *
     * @param player O jogador que trocou de arma
     */
    public static void registerWeaponSwitch(Player player) {
        UUID playerUUID = player.getUniqueId();

        // Obter a nova arma que o jogador está segurando
        ItemStack newItem = player.getInventory().getItemInHand();
        if (newItem != null) {
            Weapon newWeapon = WeaponUtils.getWeaponFromItem(newItem, player);
            if (newWeapon != null) {
                // Calcular quando o próximo tiro será permitido baseado na fireRate da nova arma
                long currentTime = System.currentTimeMillis();
                double fireRate = newWeapon.getFireRate();

                // Converter fireRate (tiros por segundo) para milissegundos entre tiros
                long timeBetweenShots = (long) (1000.0 / fireRate);

                // Definir quando o próximo tiro será permitido
                nextShotAllowedTime.put(playerUUID, currentTime + timeBetweenShots);


            }
        }

        // Garantir que o estado de disparo seja limpo
        isFiring.put(playerUUID, false);
    }



    /**
     * Inicia o processo de disparo contínuo da arma.
     *
     * Este método:
     * 1. Dispara imediatamente o primeiro tiro para evitar delay
     * 2. Configura um comportamento específico para shotguns e snipers (tiro único com cooldown)
     * 3. Para outras armas, configura um loop de disparo contínuo baseado na fireRate
     * 4. Gerencia o estado de disparo do jogador
     * 5. Permite interromper o disparo para recarregar imediatamente
     *
     * @param player O jogador que está disparando a arma
     */
    public void startFiring(Player player) {
        // Verificar se o jogador está tentando recarregar (shift + clique direito)
        if (player.isSneaking() && canReload(player)) {
            // Cancelar qualquer estado de disparo e iniciar recarga
            UUID playerUUID = player.getUniqueId();
            isFiring.put(playerUUID, false);
            reload(player);
            return;
        }

        // Atirar se a arma puder disparar
        if (canShoot(player)) {
            shoot(player);

            // Caso especial para shotgun e sniper: tiro único
            if (type == WeaponType.SHOTGUN || type == WeaponType.SNIPER) {
                isFiring.put(player.getUniqueId(), true);

                // Agendar fim do estado de disparo baseado na fireRate
                long delayTicks = Math.max(10, (long)(20.0 / fireRate));
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        isFiring.put(player.getUniqueId(), false);
                    }
                }.runTaskLater(Main.getPlugin(), delayTicks);

                return;
            }
        }

        // Disparo contínuo para outras armas
        new BukkitRunnable() {
            private final double shotsPerTick = fireRate / 20.0;
            private double shotAccumulator = 0.0;
            private boolean firstShot = true;

            @Override
            public void run() {
                // Verificar se o jogador está tentando recarregar
                if (player.isSneaking() && canReload(player)) {
                    isFiring.put(player.getUniqueId(), false);
                    reload(player);
                    cancel();
                    return;
                }

                // Verificar se pode continuar atirando
                if (!isFiring.getOrDefault(player.getUniqueId(), false) || isReloading(player)) {
                    isFiring.put(player.getUniqueId(), false);
                    cancel();
                    return;
                }

                // Verificar munição
                if (currentAmmo <= 0) {
                    // Não fazer nada com o NBT aqui, a munição é gerenciada pelo cache
                    return;
                }

                // Pular primeiro tiro (já disparado)
                if (firstShot) {
                    firstShot = false;
                    return;
                }

                // Calcular quantos tiros neste tick
                shotAccumulator += shotsPerTick;
                int shotsThisTick = (int) shotAccumulator;
                shotAccumulator -= shotsThisTick;

                // Disparar
                for (int i = 0; i < shotsThisTick && canShoot(player); i++) {
                    shoot(player);
                }
            }
        }.runTaskTimer(Main.getPlugin(), 0L, 1L);
    }

    // Adiciona método utilitário para identificar blocos finos
    private boolean isThinBlock(Block block) {
        switch (block.getType()) {
            case CARPET:
            case THIN_GLASS:
            case STAINED_GLASS_PANE:
            case FENCE:
            case FENCE_GATE:
            case IRON_FENCE:
            case NETHER_FENCE:
            case LADDER:
            case SNOW:
            case TORCH:
            case LEVER:
            case STONE_BUTTON:
            case WOOD_BUTTON:
            case TRIPWIRE:
            case TRIPWIRE_HOOK:
            case RAILS:
            case DETECTOR_RAIL:
            case ACTIVATOR_RAIL:
            case POWERED_RAIL:
            case WALL_SIGN:
            case SIGN_POST:
            case STONE_PLATE:
            case WOOD_PLATE:
            case DAYLIGHT_DETECTOR:
            case DAYLIGHT_DETECTOR_INVERTED:
            case STEP:
            case WOOD_STEP:
            case DOUBLE_STEP:
            case WOOD_DOUBLE_STEP:
                return true;
            default:
                return false;
        }
    }
}