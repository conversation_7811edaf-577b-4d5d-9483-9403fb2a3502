package org.esg.systems;

import net.minecraft.server.v1_8_R3.EnumParticle;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.esg.Effects.ParticleCat;
import org.esg.enums.WeaponType;

import java.util.Random;

/**
 * Sistema que gerencia efeitos visuais aprimorados para armas.
 * Inclui efeitos de flash no cano, impacto em diferentes materiais, etc.
 */
public class VisualEffectsSystem {

    private static final Random random = new Random();

    /**
     * Calcula a posição da mão direita do jogador.
     * Este método é uma cópia do método na classe Weapon para manter a consistência.
     *
     * @param player O jogador
     * @return A localização da mão direita do jogador
     */
    private static Location getHandLocation(Player player) {
        Location eyeLocation = player.getEyeLocation();
        Vector direction = eyeLocation.getDirection().normalize();

        // Calcular a posição da mão direita do jogador
        // 1. Obter o vetor "direita" perpendicular à direção do olhar
        Vector rightVector = new Vector(-direction.getZ(), 0, direction.getX()).normalize();

        // 2. Criar a localização da mão direita
        Location handLocation = eyeLocation.clone();

        // Ajustar para baixo (em relação aos olhos)
        handLocation.setY(handLocation.getY() - 0.5);

        // Ajustar para a direita do jogador
        handLocation.add(rightVector.multiply(0.5));

        // Ajustar ligeiramente para frente e para o lado
        // Isso posiciona a mão um pouco à frente do jogador e mais para o lado
        handLocation.add(direction.clone().multiply(0.3));

        return handLocation;
    }

    /**
     * Cria um efeito de flash no cano da arma.
     *
     * @param player O jogador que disparou a arma
     * @param weaponType O tipo de arma
     */
    public static void createMuzzleFlash(Player player, WeaponType weaponType) {
        try {
            // Obter a localização da mão direita do jogador usando o mesmo método da classe Weapon
            Location handLocation = getHandLocation(player);

            // Obter a direção para onde o jogador está olhando
            Vector direction = player.getEyeLocation().getDirection().normalize();

            // Localização ligeiramente à frente para o flash do cano
            Location muzzleLocation = handLocation.clone().add(direction.clone().multiply(0.3));

            // Criar partículas de flash com base no tipo de arma
            switch (weaponType) {
                case SNIPER:
                    // Flash grande e brilhante para sniper - reduzido
                    ParticleCat.sendParticle(EnumParticle.FLAME, muzzleLocation, 0, 0, 0, 0.01f, 4); // reduzido de 8
                    ParticleCat.sendParticle(EnumParticle.SMOKE_LARGE, muzzleLocation, 0, 0, 0, 0.01f, 3); // reduzido de 5
                    ParticleCat.sendParticle(EnumParticle.FIREWORKS_SPARK, muzzleLocation, 0, 0, 0, 0.03f, 1); // reduzido de 3

                    // Efeito de recuo reduzido
                    Location recoilLoc = handLocation.clone().add(direction.clone().multiply(-0.2));
                    ParticleCat.sendParticle(EnumParticle.SMOKE_NORMAL, recoilLoc, 0.03f, 0.03f, 0.03f, 0.01f, 1); // reduzido de 3
                    break;

                case RIFLE:
                    // Flash médio para rifle - reduzido
                    ParticleCat.sendParticle(EnumParticle.FLAME, muzzleLocation, 0, 0, 0, 0.01f, 2); // reduzido de 5
                    ParticleCat.sendParticle(EnumParticle.SMOKE_NORMAL, muzzleLocation, 0, 0, 0, 0.01f, 1); // reduzido de 3
                    ParticleCat.sendParticle(EnumParticle.FIREWORKS_SPARK, muzzleLocation, 0, 0, 0, 0.02f, 1); // reduzido de 2
                    break;

                case SHOTGUN:
                    // Flash grande e espalhado para shotgun - reduzido
                    ParticleCat.sendParticle(EnumParticle.FLAME, muzzleLocation, 0.03f, 0.03f, 0.03f, 0.01f, 5); // reduzido de 10
                    ParticleCat.sendParticle(EnumParticle.SMOKE_LARGE, muzzleLocation, 0.03f, 0.03f, 0.03f, 0.01f, 3); // reduzido de 8
                    ParticleCat.sendParticle(EnumParticle.FIREWORKS_SPARK, muzzleLocation, 0.03f, 0.03f, 0.03f, 0.03f, 2); // reduzido de 5

                    // Efeito de recuo forte reduzido
                    Location shotgunRecoilLoc = handLocation.clone().add(direction.clone().multiply(-0.3));
                    ParticleCat.sendParticle(EnumParticle.SMOKE_LARGE, shotgunRecoilLoc, 0.03f, 0.03f, 0.03f, 0.01f, 2); // reduzido de 5
                    break;

                case SMG:
                    // Flash pequeno e rápido para SMG - reduzido
                    ParticleCat.sendParticle(EnumParticle.FLAME, muzzleLocation, 0, 0, 0, 0.01f, 1); // reduzido de 3
                    if (Math.random() < 0.5) { // 50% de chance de mostrar fumaça
                        ParticleCat.sendParticle(EnumParticle.SMOKE_NORMAL, muzzleLocation, 0, 0, 0, 0.01f, 1); // reduzido de 2
                    }
                    // Removido o efeito de fogos de artifício para SMG
                    break;

                case PISTOL:
                    // Flash pequeno para pistola - reduzido
                    ParticleCat.sendParticle(EnumParticle.FLAME, muzzleLocation, 0, 0, 0, 0.01f, 1); // reduzido de 2
                    if (Math.random() < 0.3) { // Apenas 30% de chance de mostrar fumaça
                        ParticleCat.sendParticle(EnumParticle.SMOKE_NORMAL, muzzleLocation, 0, 0, 0, 0.01f, 1);
                    }
                    break;
            }
        } catch (Exception e) {
            // Ignorar erros de partículas para evitar crashes
        }
    }

    /**
     * Cria efeitos de impacto em um bloco.
     *
     * @param location A localização do impacto
     * @param block O bloco atingido
     */
    public static void createBlockImpactEffect(Location location, Block block) {
        if (block == null) return;

        Material material = block.getType();

        try {
            // Chance aleatória para reduzir ainda mais as partículas (70% de chance)
            if (Math.random() > 0.7) {
                return;
            }

            // Usar partículas de block break do próprio bloco atingido
            if (material == Material.WATER || material == Material.STATIONARY_WATER) {
                // Impacto em água - manter splash
                ParticleCat.sendParticle(EnumParticle.WATER_SPLASH, location, 0.05f, 0.05f, 0.05f, 0.05f, 5); // reduzido de 15
            } else if (material == Material.AIR) {
                // Não mostrar partículas para ar
                return;
            } else {
                // Usar partícula de block break do material atingido
                ParticleCat.sendBlockBreakParticle(material, location, 0.03f, 0.03f, 0.03f, 0.01f, 3);
            }
        } catch (Exception e) {
            // Ignorar erros de partículas para evitar crashes
        }
    }

    /**
     * Cria efeitos de impacto em uma entidade.
     *
     * @param location A localização do impacto
     * @param target A entidade atingida
     * @param isHeadshot Se o tiro foi um headshot (apenas para snipers)
     */
    public static void createEntityImpactEffect(Location location, LivingEntity target, boolean isHeadshot) {
        try {
            // Efeito de block break do bloco de redstone - reduzido
            ParticleCat.sendBlockBreakParticle(Material.REDSTONE_BLOCK, location, 0.05f, 0.05f, 0.05f, 0, 5); // reduzido de 10

            // Efeito adicional para headshot - também reduzido
            if (isHeadshot) {
                ParticleCat.sendBlockBreakParticle(Material.REDSTONE_BLOCK, location, 0.1f, 0.1f, 0.1f, 0, 8); // reduzido de 20
                ParticleCat.sendParticle(EnumParticle.CRIT, location, 0.05f, 0.05f, 0.05f, 0.05f, 4); // reduzido de 10
            }
        } catch (Exception e) {
            // Ignorar erros de partículas para evitar crashes
        }
    }
}
