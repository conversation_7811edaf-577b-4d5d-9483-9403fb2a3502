package org.esg.weapons;

import org.esg.enums.AmmoType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;

public class <PERSON><PERSON><PERSON> extends Weapon {

    public Dragunov() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, Contador<PERSON>roj<PERSON>is, MultiplicadorHeadshot
        super("<PERSON><PERSON><PERSON>", WeaponType.SNIPER, AmmoType._762MM, 12, 180, 0.9, 1.2, 165, 10, 10, 3, 1, 2.0);

        // A <PERSON><PERSON>nov tem:
        // - <PERSON><PERSON> base alto (12, menos que a Barrett)
        // - Multiplicador de distância: 0.6x a curta distância, 1.5x a longa distância
        // - <PERSON><PERSON> mínimo a curta distância: 7.2 (12 * 0.6)
        // - <PERSON>o máximo a longa distância: 18.0 (12 * 1.5)
        // - Alcance alto (180 vs 200 da <PERSON>)
        // - <PERSON><PERSON><PERSON> alta (0.9 vs 1.0 da <PERSON>)
        // - Velocidade de tiro maior que a Barrett (1.2 vs 0.5 da <PERSON>)
        // - Velocidade de projétil alta (140 vs 150 da Barrett)
        // - 1 projétil por tiro
        // - Multiplicador de headshot: 2.0x
        // - Capacidade de munição maior (10 vs 5 da Barrett)
        // - Tempo de recarga menor (3 vs 4 da Barrett)
    }
}