package org.esg.weapons;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.esg.Main;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.utils.WeaponUtils;
import net.minecraft.server.v1_8_R3.EnumParticle;

import java.util.Collection;
import java.util.logging.Logger;

/**
 * Implementação de um lançador de foguetes (RPG)
 * com efeito de queda balística e explosão ao impacto.
 */
public class RPG extends Weapon {
    private static final Logger LOGGER = Logger.getLogger(RPG.class.getName());
    private static final double GRAVITY = 0.008; // Fator de queda do projétil (reduzido)
    private static final double GRAVITY_DELAY = 10; // Atraso antes da gravidade começar a agir (em ticks)
    private static final double TRACE_STEP = 0.8; // Distância entre passos de simulação
    private static final int EXPLOSION_RADIUS = 3; // Raio da explosão em blocos
    private static final double MAX_KNOCKBACK = 5.0; // Knockback máximo em blocos (reduzido ainda mais)
    private static final double KNOCKBACK_Y_FACTOR = 0.4; // Fator de redução do knockback vertical
    private static final double KNOCKBACK_H_FACTOR = 0.7; // Fator de aumento do knockback horizontal
    private static final double ENTITY_DETECTION_RADIUS = 1.0; // Raio para detecção de entidades (aumentado)

    public RPG() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil,
        // MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis
        super("RPG", WeaponType.SNIPER, AmmoType._50CAL, 35.0, 200.0, 0.9, 0.5, 75.0, 1, 1, 5, 1);
    }

    @Override
    public void shoot(Player player) {
        if (!canShoot(player)) {
            return;
        }

        if (currentAmmo <= 0) {
            // Se não tem munição, iniciar recarga automaticamente
            reload(player);
            return;
        }

        // Reduzir munição e atualizar o estado no item
        this.currentAmmo--;
        try {
            WeaponUtils.updateWeaponInHand(player, this);
            org.esg.utils.AmmoCache.setAmmo(player, player.getInventory().getItemInHand(), this.currentAmmo);
            org.esg.utils.AmmoCache.syncWithNBT(player, player.getInventory().getItemInHand(), true);
            LOGGER.info("RPG: Munição reduzida para " + currentAmmo);
        } catch (Exception e) {
            LOGGER.warning("RPG: Erro ao atualizar munição: " + e.getMessage());
        }

        // Efeitos de som e visuais do disparo
        player.getWorld().playSound(player.getLocation(), Sound.EXPLODE, 0.5f, 2.0f);

        // Obter a direção do tiro
        Location eyeLocation = player.getEyeLocation();
        Vector direction = eyeLocation.getDirection();

        // Iniciar trajetória do projétil
        launchRocket(player, eyeLocation, direction);

        // Remover a RPG do inventário após o disparo (arma de uso único)
        player.getInventory().setItemInHand(null);
    }

    @Override
    public void reload(Player player) {
        super.reload(player);
        // Após recarga, garantir atualização do NBT e do cache
        try {
            this.currentAmmo = this.maxAmmo;
            WeaponUtils.updateWeaponInHand(player, this);
            org.esg.utils.AmmoCache.setAmmo(player, player.getInventory().getItemInHand(), this.currentAmmo);
            org.esg.utils.AmmoCache.syncWithNBT(player, player.getInventory().getItemInHand(), true);
        } catch (Exception e) {
            LOGGER.warning("RPG: Erro ao atualizar munição após recarga: " + e.getMessage());
        }
    }

    private void launchRocket(Player player, Location startLocation, Vector direction) {
        World world = startLocation.getWorld();
        final Location projectileLocation = startLocation.clone().add(direction.clone().multiply(1.2));
        final Vector velocity = direction.clone().multiply(projectileSpeed / 20.0);

        new BukkitRunnable() {
            private double distanceTraveled = 0.0;
            private Location lastLocation = projectileLocation.clone();
            private int ticksElapsed = 0;

            @Override
            public void run() {
                ticksElapsed++;

                Location from = projectileLocation.clone();
                projectileLocation.add(velocity);
                Location to = projectileLocation.clone();
                distanceTraveled += TRACE_STEP;

                // Subpassos para checagem de colisão precisa
                int steps = 10;
                for (int i = 1; i <= steps; i++) {
                    double t = i / (double) steps;
                    Location interp = from.clone().add(to.clone().subtract(from.toVector()).multiply(t));
                    Block block = interp.getBlock();
                    if (block.getType() != Material.AIR && block.getType() != Material.WATER && block.getType() != Material.STATIONARY_WATER) {
                        explode(interp, player);
                        cancel();
                        return;
                    }
                }

                // Verificar se atingiu alcance máximo
                if (distanceTraveled >= range) {
                    explode(projectileLocation, player);
                    cancel();
                    return;
                }

                // Mostrar partículas na trajetória
                if (distanceTraveled % 4 < 1) {
                    sendParticle(EnumParticle.SMOKE_NORMAL, projectileLocation, 0f, 0f, 0f, 0f, 1);
                    sendParticle(EnumParticle.FLAME, projectileLocation, 0f, 0f, 0f, 0f, 1);
                }

                // Verificar colisão com entidades
                Collection<Entity> nearbyEntities = world.getNearbyEntities(
                        projectileLocation, ENTITY_DETECTION_RADIUS, ENTITY_DETECTION_RADIUS, ENTITY_DETECTION_RADIUS);
                for (Entity entity : nearbyEntities) {
                    if (entity instanceof LivingEntity && entity != player) {
                        explode(projectileLocation, player);
                        cancel();
                        return;
                    }
                }

                lastLocation = projectileLocation.clone();
            }
        }.runTaskTimer(Main.getPlugin(), 0L, 1L);
    }

    private void explode(Location location, Player shooter) {
        World world = location.getWorld();
        world.playSound(location, Sound.EXPLODE, 1.0f, 0.8f);
        sendParticle(EnumParticle.EXPLOSION_LARGE, location, 0f, 0f, 0f, 0f, 1);
        sendParticle(EnumParticle.FLAME, location, 1.0f, 1.0f, 1.0f, 0.1f, 15);
        sendParticle(EnumParticle.SMOKE_LARGE, location, 1.5f, 1.5f, 1.5f, 0.1f, 20);

        // Knockback no player que atirou (recuo)
        Vector away = shooter.getLocation().toVector().subtract(location.toVector()).normalize();
        away.setY(0.3); // leve impulso para cima
        shooter.setVelocity(shooter.getVelocity().add(away.multiply(1.2)));

        // Destruir blocos dentro do raio da explosão (exceto ar)
        int radius = EXPLOSION_RADIUS;
        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    Location checkLoc = location.clone().add(x, y, z);
                    if (checkLoc.distance(location) <= radius + 0.5) {
                        Block block = world.getBlockAt(checkLoc);
                        Material type = block.getType();
                        byte data = block.getData();
                        if (type == Material.SMOOTH_BRICK && data == 0) {
                            block.setType(Material.SMOOTH_BRICK);
                            block.setData((byte) 2); // vira cracked stone bricks
                        } else if (type == Material.SMOOTH_BRICK && data == 2) {
                            block.setType(Material.AIR); // cracked é destruído
                        } else if (type != Material.AIR) {
                            block.setType(Material.AIR);
                        }
                    }
                }
            }
        }

        // Encontrar entidades na área da explosão
        Collection<Entity> nearbyEntities = world.getNearbyEntities(
                location, EXPLOSION_RADIUS, EXPLOSION_RADIUS, EXPLOSION_RADIUS);

        for (Entity entity : nearbyEntities) {
            if (entity instanceof LivingEntity && entity != shooter) {
                LivingEntity target = (LivingEntity) entity;
                double distance = location.distance(target.getLocation());
                // Dano maior quanto mais perto do centro
                double damageMultiplier = 1.0 - (distance / (EXPLOSION_RADIUS + 0.5));
                damageMultiplier = Math.max(0.2, damageMultiplier); // No mínimo 20%
                double explosionDamage = damage * (damageMultiplier + 0.5); // bônus de dano de perto
                target.damage(explosionDamage, shooter);

                // Knockback igual ao anterior
                double knockbackMultiplier = 1.0 - (distance / EXPLOSION_RADIUS);
                double knockbackForce = MAX_KNOCKBACK * knockbackMultiplier;
                Vector knockback = new Vector(0, knockbackForce * KNOCKBACK_Y_FACTOR, 0);
                if (distance > 0.1) {
                    Vector awayDirection = target.getLocation().toVector().subtract(location.toVector()).normalize();
                    awayDirection.multiply(knockbackForce * KNOCKBACK_H_FACTOR);
                    awayDirection.setY(0);
                    knockback.add(awayDirection);
                }
                target.setVelocity(target.getVelocity().add(knockback));

                // PvP Tag para evitar combat logging
                if (target instanceof Player && shooter instanceof Player) {
                    try {
                        Class.forName("org.esg.utils.PvPTagUtil")
                             .getMethod("tagPlayers", Player.class, Player.class)
                             .invoke(null, shooter, (Player)target);
                    } catch (Exception e) {
                        LOGGER.warning("Não foi possível aplicar a tag PvP: " + e.getMessage());
                    }
                }
            }
        }
    }

    // Método auxiliar para enviar partículas
    private void sendParticle(EnumParticle type, Location location, float xOffset, float yOffset, float zOffset, float speed, int count) {
        for (Player player : location.getWorld().getPlayers()) {
            if (player.getLocation().distance(location) <= 50) {
                org.esg.Effects.ParticleCat.sendParticleTo(type, player, location, xOffset, yOffset, zOffset, speed, count);
            }
        }
    }
}