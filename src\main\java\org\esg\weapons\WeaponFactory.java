package org.esg.weapons;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;
import org.esg.weapons.M4A1;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import java.util.logging.Logger;

/**
 * Factory class for creating and managing weapons.
 */
public final class WeaponFactory {

    private static final Logger LOGGER = Logger.getLogger(WeaponFactory.class.getName());
    private static final Map<String, Supplier<Weapon>> WEAPON_REGISTRY = new HashMap<>();
    private static final Map<String, Material> WEAPON_MATERIALS = new HashMap<>();

    static {
        // Definir materiais específicos para cada arma
        WEAPON_MATERIALS.put("ak-47", Material.STONE_HOE);
        WEAPON_MATERIALS.put("ar-15", Material.IRON_HOE);
        WEAPON_MATERIALS.put("spas-12", Material.WOOD_HOE);
        WEAPON_MATERIALS.put("barrett", Material.DIAMOND_HOE);
        WEAPON_MATERIALS.put("barret", Material.DIAMOND_HOE); // Variação com um "t"
        WEAPON_MATERIALS.put("uzi", Material.GOLD_HOE);
        // Novos materiais para as armas adicionadas
        WEAPON_MATERIALS.put("dragunov", Material.DIAMOND_SPADE);
        WEAPON_MATERIALS.put("lança-chamas", Material.RECORD_10);
        WEAPON_MATERIALS.put("3oitão", Material.GOLD_AXE);
        // Material para M4A1
        WEAPON_MATERIALS.put("m4a1", Material.COMPASS);
        // Material para RPG
        WEAPON_MATERIALS.put("rpg", Material.SADDLE);
        // Material para DMR
        WEAPON_MATERIALS.put("dmr", Material.CARROT_STICK);

        // Registrar armas com seus nomes exatos (case-sensitive)
        registerWeapon("AK-47", AK47::new);
        registerWeapon("Spas-12", Shotgun::new);
        registerWeapon("AR-15", AR15::new);
        registerWeapon("Barrett", Barrett::new);
        registerWeapon("UZI", UZI::new);
        // Registrar as novas armas
        registerWeapon("Dragunov", Dragunov::new);
        registerWeapon("Lança-Chamas", LancaChamas::new);
        registerWeapon("3oitão", Oitao::new);
        // Registrar a M4A1
        registerWeapon("M4A1", M4A1::new);
        // Registrar o RPG
        registerWeapon("RPG", RPG::new);
        // Registrar o DMR
        registerWeapon("DMR", DMR::new);

        // Garantir que a Barrett está registrada corretamente com variações de nome
        WEAPON_REGISTRY.put("barrett", WEAPON_REGISTRY.get("Barrett"));
        // Adicionar variações comuns para Barrett com grafia diferente
        WEAPON_REGISTRY.put("barret", WEAPON_REGISTRY.get("Barrett"));
        WEAPON_REGISTRY.put("BARRET", WEAPON_REGISTRY.get("Barrett"));
        WEAPON_REGISTRY.put("BARRETT", WEAPON_REGISTRY.get("Barrett"));

        // Log de armas registradas
        LOGGER.info("Armas registradas: " + WEAPON_REGISTRY.keySet());
    }

    private WeaponFactory() {
        // Construtor privado para evitar instanciação.
    }

    public static void registerWeapon(String name, Supplier<Weapon> weaponSupplier) {
        // Registrar com o nome exato para preservar a formatação
        WEAPON_REGISTRY.put(name, weaponSupplier);
        // Também registrar com o nome em minúsculas para facilitar a busca
        WEAPON_REGISTRY.put(name.toLowerCase(), weaponSupplier);
    }

    public static Weapon createWeapon(String weaponName) {
        // Log especial para Barrett
        if (weaponName != null && (weaponName.equalsIgnoreCase("Barrett") || weaponName.equalsIgnoreCase("BARRET") || weaponName.equalsIgnoreCase("Barret"))) {
            LOGGER.info("Tentando criar Barrett/Barret. Fornecedor disponível: " +
                       (WEAPON_REGISTRY.containsKey("Barrett") || WEAPON_REGISTRY.containsKey("barrett") ||
                        WEAPON_REGISTRY.containsKey("BARRET") || WEAPON_REGISTRY.containsKey("barret")));
        }

        // Procurar a arma pelo nome exato primeiro
        Supplier<Weapon> supplier = WEAPON_REGISTRY.get(weaponName);

        // Se não encontrar, tentar com o nome em minúsculas
        if (supplier == null) {
            supplier = WEAPON_REGISTRY.get(weaponName.toLowerCase());
            if (supplier != null) {
                LOGGER.info("Arma encontrada com nome em minúsculas: " + weaponName.toLowerCase());
            }
        }

        // Se ainda não encontrar, tentar com variações comuns
        if (supplier == null) {
            // Remover hífens e espaços
            String simplifiedName = weaponName.toLowerCase().replace("-", "").replace(" ", "");
            LOGGER.info("Tentando nome simplificado: " + simplifiedName);

            // Verificar variações comuns
            switch (simplifiedName) {
                case "ak47": supplier = WEAPON_REGISTRY.get("AK-47"); break;
                case "spas12": supplier = WEAPON_REGISTRY.get("Spas-12"); break;
                case "ar15": supplier = WEAPON_REGISTRY.get("AR-15"); break;
                case "lancachamas": supplier = WEAPON_REGISTRY.get("Lança-Chamas"); break;
                case "oitao":
                case "3oitao": supplier = WEAPON_REGISTRY.get("3oitão"); break;
                case "barrett":
                case "barret": supplier = WEAPON_REGISTRY.get("Barrett"); break;
                case "m4a1": supplier = WEAPON_REGISTRY.get("M4A1"); break;
                case "rpg": supplier = WEAPON_REGISTRY.get("RPG"); break;
                case "dmr": supplier = WEAPON_REGISTRY.get("DMR"); break;
            }
        }

        // Caso especial para Barrett
        if (supplier == null && weaponName != null &&
            (weaponName.equalsIgnoreCase("Barrett") ||
             weaponName.equalsIgnoreCase("BARRET") ||
             weaponName.equalsIgnoreCase("Barret"))) {
            LOGGER.warning("Fornecedor para Barrett/Barret não encontrado, criando manualmente");
            return new Barrett();  // Criar diretamente se não encontrar
        }

        if (supplier == null) {
            LOGGER.warning("Arma desconhecida: " + weaponName);
            throw new IllegalArgumentException("Arma desconhecida: " + weaponName);
        }

        Weapon weapon = supplier.get();
        // Comentado para reduzir log spam
        // LOGGER.info("Arma criada: " + weapon.getName() + " (Tipo: " + weapon.getType() + ")");
        return weapon;
    }

    public static ItemStack toItemStack(Weapon weapon) {
        ItemStack item = new ItemStack(getMaterialForWeapon(weapon));
        ItemMeta meta = item.getItemMeta();
        if (meta != null) { meta.setDisplayName("§r" + weapon.getName()); item.setItemMeta(meta); }
        return item;
    }

    public static Map<String, Supplier<Weapon>> getWeaponRegistry() {
        return Collections.unmodifiableMap(WEAPON_REGISTRY);
    }

    private static Material getMaterialForWeapon(Weapon weapon) {
        if (weapon == null) return Material.IRON_HOE;

        String name = weapon.getName().toLowerCase().replace("-", "").replace(" ", "");
        Material material;
        switch (name) {
            case "ak47": material = WEAPON_MATERIALS.get("ak-47"); break;
            case "ar15": material = WEAPON_MATERIALS.get("ar-15"); break;
            case "spas12": material = WEAPON_MATERIALS.get("spas-12"); break;
            case "barrett":
            case "barret": material = Material.DIAMOND_HOE; break;
            case "uzi": material = WEAPON_MATERIALS.get("uzi"); break;
            case "dragunov": material = WEAPON_MATERIALS.get("dragunov"); break;
            case "lançachamas": material = WEAPON_MATERIALS.get("lança-chamas"); break;
            case "3oitão": material = WEAPON_MATERIALS.get("3oitão"); break;
            case "m4a1": material = WEAPON_MATERIALS.get("m4a1"); break;
            case "rpg": material = WEAPON_MATERIALS.get("rpg"); break;
            case "dmr": material = WEAPON_MATERIALS.get("dmr"); break;
            default: material = WEAPON_MATERIALS.get(name); break;
        }
        return material != null ? material : Material.IRON_HOE;
    }
}