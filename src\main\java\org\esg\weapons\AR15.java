package org.esg.weapons;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.utils.KnockbackUtil;
import org.esg.Manager.KnockbackImmunityManager;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Logger;

public class AR15 extends Weapon {

    private static final Logger LOGGER = Logger.getLogger(AR15.class.getName());

    // Track consecutive hits for gradual lifting effect
    private static final Map<UUID, Integer> consecutiveHits = new HashMap<>();
    private static final Map<UUID, Long> lastHitTime = new HashMap<>();
    private static final long HIT_RESET_TIME = 2500; // Reset consecutive hits after 2.5 seconds
    private static final int MAX_CONSECUTIVE_HITS = 6; // Maximum stacking effect para efeito fly

    public AR15() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis
        super("AR-15", WeaponType.RIFLE, AmmoType._556MM, 1.3, 100, 0.85, 6, 120, 30, 30, 3, 1);

        // A AR-15 tem:
        // - Dano menor que a AK47 (0.08 vs 0.1 da AK47)
        // - Alcance maior (90 vs 80 da AK47)
        // - Precisão muito maior (0.85 vs 0.7 da AK47) - Isso causa um espalhamento menor
        // - Velocidade de tiro um pouco menor (8 vs 10 da AK47)
        // - Velocidade de projétil maior (100 vs 90 da AK47)
        // - 1 projétil por tiro
        // - Rifle de alta precisão, ideal para tiros de média-longa distância
        // - Efeito especial: Elevação gradual com tiros consecutivos
    }

    public static Vector calculateAR15Knockback(Player target, Player attacker) {
        UUID targetId = target.getUniqueId();
        long currentTime = System.currentTimeMillis();

        Long lastHit = lastHitTime.get(targetId);
        if (lastHit == null || (currentTime - lastHit) > HIT_RESET_TIME) consecutiveHits.put(targetId, 0);

        int hits = Math.min(consecutiveHits.getOrDefault(targetId, 0) + 1, MAX_CONSECUTIVE_HITS);
        consecutiveHits.put(targetId, hits);
        lastHitTime.put(targetId, currentTime);

        Vector baseKnockback = KnockbackUtil.getKnockback(target.getLocation(), attacker, "AR-15");
        double progressiveMultiplier = 1.3 + (hits * 0.15);
        Vector attackerToTarget = target.getLocation().toVector().subtract(attacker.getLocation().toVector()).normalize();

        // Aplicar efeito progressivo tanto horizontal quanto vertical
        // Horizontal: empurra para trás na direção oposta ao atacante (suavizado ainda mais)
        baseKnockback.setX(attackerToTarget.getX() * 0.17 * progressiveMultiplier);
        baseKnockback.setZ(attackerToTarget.getZ() * 0.17 * progressiveMultiplier);

        // Vertical: empurra para cima progressivamente (suavizado - reduzido significativamente)
        baseKnockback.setY(0.17 * progressiveMultiplier); // Força vertical progressiva mais suave



        return baseKnockback;
    }

    /**
     * Clean up tracking data for disconnected players
     */
    public static void cleanupPlayerData(UUID playerId) {
        consecutiveHits.remove(playerId);
        lastHitTime.remove(playerId);
    }
}
